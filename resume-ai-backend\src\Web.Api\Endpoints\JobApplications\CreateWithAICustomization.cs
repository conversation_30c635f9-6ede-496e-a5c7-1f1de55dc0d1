using Application.Abstractions.Messaging;
using Application.JobApplications.CreateWithAICustomization;
using Domain.AI;
using SharedKernel;
using Web.Api.Extensions;
using Web.Api.Infrastructure;

namespace Web.Api.Endpoints.JobApplications;

internal sealed class CreateWithAICustomization : IEndpoint
{
    public sealed record CreateJobApplicationWithAICustomizationRequest(
        Guid JobId,
        Guid CustomizedResumeId,
        string AICustomizedContent,
        string AICustomizationSummary,
        double AIConfidenceScore,
        AIModelType? AIModelUsed = null);

    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("job-applications/ai-customized", async (
            CreateJobApplicationWithAICustomizationRequest request,
            ICommandHandler<CreateJobApplicationWithAICustomizationCommand, Guid> handler,
            CancellationToken cancellationToken) =>
        {
            // The user ID will be resolved in the command handler through IUserContext
            var command = new CreateJobApplicationWithAICustomizationCommand(
                request.JobId,
                Guid.Empty, // This will be set by the handler using IUserContext
                request.CustomizedResumeId,
                request.AICustomizedContent,
                request.AICustomizationSummary,
                request.AIConfidenceScore,
                request.AIModelUsed);

            Result<Guid> result = await handler.Handle(command, cancellationToken);

            return result.Match(
                jobApplicationId => Results.Created($"/job-applications/{jobApplicationId}", jobApplicationId),
                CustomResults.Problem);
        })
        .WithTags(Tags.JobApplications)
        .WithName("CreateJobApplicationWithAICustomization")
        .Produces<Guid>(StatusCodes.Status201Created)
        .ProducesValidationProblem()
        .RequireAuthorization();
    }
}
