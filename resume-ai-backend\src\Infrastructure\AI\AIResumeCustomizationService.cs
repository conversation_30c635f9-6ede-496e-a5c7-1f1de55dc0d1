using System.Text.Json;
using Application.Abstractions.AI;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.Google;
using SharedKernel;

namespace Infrastructure.AI;

internal sealed class AIResumeCustomizationService(
    IChatCompletionService chatCompletionService,
    IPromptService promptService,
    ILogger<AIResumeCustomizationService> logger,
    IOptions<AIServiceOptions> options)
    : IAIResumeCustomizationService
{
    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        PropertyNameCaseInsensitive = true
    };

    private readonly AIServiceOptions _options = options.Value;

    public async Task<Result<AICustomizationResponse>> CustomizeResumeAsync(
        AICustomizationRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("Starting AI resume customization for job: {JobTitle}", request.JobTitle);

            // Validate input
            if (string.IsNullOrWhiteSpace(request.OriginalResumeContent))
            {
                return Result.Failure<AICustomizationResponse>(AIServiceErrors.InvalidContent());
            }

            // Determine job category for specialized instructions
            _ = DetermineJobCategory(request.JobTitle, request.JobDescription);

            // Build chat messages using prompt service
            string systemMessage = promptService.GetSystemMessage();
            string userPrompt = promptService.GetUserPrompt(
                request.JobTitle,
                request.CompanyUrl,
                request.JobDescription,
                request.OriginalResumeContent);

            // Create chat history for Semantic Kernel
            var chatHistory = new ChatHistory();
            chatHistory.AddSystemMessage(systemMessage);
            chatHistory.AddUserMessage(userPrompt);

            logger.LogDebug("Sending request to Gemini API via Semantic Kernel with model: {Model}", _options.Model);

            // Create prompt execution settings
            var executionSettings = new GeminiPromptExecutionSettings
            {
                ResponseMimeType = "application/json"
            };

            // Create a timeout cancellation token
            using var timeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(_options.TimeoutSeconds));
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);

            // Call Semantic Kernel chat completion
            var response = await chatCompletionService.GetChatMessageContentAsync(
                chatHistory,
                executionSettings,
                cancellationToken: combinedCts.Token);

            if (response?.Content is null || string.IsNullOrEmpty(response.Content))
            {
                logger.LogError("Semantic Kernel returned empty response");
                return Result.Failure<AICustomizationResponse>(AIServiceErrors.InvalidResponse());
            }

            // Parse the AI response
            string responseText = response.Content;
            AICustomizationResponse customizationResponse = ParseAIResponse(responseText);

            // Validate response quality
            QualityControlConfig qualityControl = promptService.GetConfiguration().QualityControl;
            if (customizationResponse.ConfidenceScore < qualityControl.MinConfidenceThreshold)
            {
                logger.LogWarning("AI customization confidence {Confidence} below threshold {Threshold}",
                    customizationResponse.ConfidenceScore, qualityControl.MinConfidenceThreshold);
            }

            if (customizationResponse.CustomizedResumeContent.Length > qualityControl.MaxContentLength)
            {
                logger.LogWarning("Customized content length {Length} exceeds maximum {MaxLength}",
                    customizationResponse.CustomizedResumeContent.Length, qualityControl.MaxContentLength);

                return Result.Failure<AICustomizationResponse>(AIServiceErrors.ProcessingFailed("Content too long"));
            }

            logger.LogInformation("Semantic Kernel AI resume customization completed successfully with confidence {Confidence}",
                customizationResponse.ConfidenceScore);

            return Result.Success(customizationResponse);
        }
        catch (OperationCanceledException ex)
        {
            logger.LogWarning(ex, "Semantic Kernel AI service request was cancelled or timed out");
            return Result.Failure<AICustomizationResponse>(AIServiceErrors.Timeout());
        }
        catch (JsonException ex)
        {
            logger.LogError(ex, "Failed to parse AI service response as JSON");
            return Result.Failure<AICustomizationResponse>(AIServiceErrors.InvalidResponse());
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error occurred during AI processing");
            return Result.Failure<AICustomizationResponse>(AIServiceErrors.ProcessingFailed(ex.Message));
        }
    }

    private static string? DetermineJobCategory(string jobTitle, string jobDescription)
    {
        string content = $"{jobTitle} {jobDescription}".ToUpperInvariant();

        if (content.Contains("software") || content.Contains("developer") || content.Contains("engineer") ||
            content.Contains("programming") || content.Contains("technical"))
        {
            return "technology";
        }

        if (content.Contains("marketing") || content.Contains("brand") || content.Contains("campaign") ||
            content.Contains("social media"))
        {
            return "marketing";
        }

        if (content.Contains("finance") || content.Contains("accounting") || content.Contains("financial") ||
            content.Contains("analyst"))
        {
            return "finance";
        }

        if (content.Contains("healthcare") || content.Contains("medical") || content.Contains("nurse") ||
            content.Contains("doctor") || content.Contains("clinical"))
        {
            return "healthcare";
        }

        return null; // Use default instructions
    }

    private static AICustomizationResponse ParseAIResponse(string aiResponse)
    {
        try
        {
            //Clean the response to handle markdown code blocks
            string cleanedResponse = CleanMarkdownCodeBlocks(aiResponse);

            AIResponseJson? jsonResponse = JsonSerializer.Deserialize<AIResponseJson>(cleanedResponse, JsonOptions);

            return new AICustomizationResponse(
                jsonResponse?.CustomizedContent ?? aiResponse,
                jsonResponse?.Summary ?? "AI customization completed",
                jsonResponse?.Confidence ?? 0.7);
        }
        catch
        {
            // Fallback if JSON parsing fails
            return new AICustomizationResponse(
                aiResponse,
                "AI customization completed (fallback parsing)",
                0.6);
        }
    }

    private static string CleanMarkdownCodeBlocks(string response)
    {
        if (string.IsNullOrWhiteSpace(response))
        {
            return response;
        }

        // Remove markdown code block syntax (```json and ```)
        string cleaned = response.Trim();

        // Check if response starts with ```json or ``` and ends with ```
        if (cleaned.StartsWith("```json", StringComparison.OrdinalIgnoreCase))
        {
            cleaned = cleaned[7..]; // Remove ```json
        }
        else if (cleaned.StartsWith("```", StringComparison.Ordinal))
        {
            cleaned = cleaned[3..]; // Remove ```
        }

        if (cleaned.EndsWith("```", StringComparison.Ordinal))
        {
            cleaned = cleaned[..^3]; // Remove trailing ```
        }

        return cleaned.Trim();
    }

    private sealed class AIResponseJson
    {
        public string? CustomizedContent { get; init; } = string.Empty;
        public string? Summary { get; init; } = string.Empty;
        public double Confidence { get; init; }
        public string[] KeyChanges { get; init; } = [];
    }
}

