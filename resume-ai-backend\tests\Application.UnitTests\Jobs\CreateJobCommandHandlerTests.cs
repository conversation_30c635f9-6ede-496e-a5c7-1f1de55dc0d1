using Application.Abstractions.Authentication;
using Application.Abstractions.Data;
using Application.Jobs.Create;
using Domain.AI;
using Domain.Jobs;
using Domain.Resumes;
using Domain.Users;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using NSubstitute;
using SharedKernel;

namespace Application.UnitTests.Jobs;

public class CreateJobCommandHandlerTests
{
    private readonly IApplicationDbContext _context;
    private readonly IUserContext _userContext;
    private readonly CreateJobCommandHandler _handler;

    public CreateJobCommandHandlerTests()
    {
        _context = Substitute.For<IApplicationDbContext>();
        _userContext = Substitute.For<IUserContext>();
        _handler = new CreateJobCommandHandler(_context, _userContext);
    }

    [Fact]
    public async Task Handle_WithPreferredAIModel_ShouldSetAIModelOnJob()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var resumeId = Guid.NewGuid();
        
        _userContext.UserId.Returns(userId);

        var user = new User { Id = userId, Email = "<EMAIL>" };
        var resume = new Resume { Id = resumeId, UserId = userId, ParentId = null };

        var users = new List<User> { user }.AsQueryable();
        var resumes = new List<Resume> { resume }.AsQueryable();
        var jobs = new List<Job>().AsQueryable();

        var mockUserSet = Substitute.For<DbSet<User>>();
        var mockResumeSet = Substitute.For<DbSet<Resume>>();
        var mockJobSet = Substitute.For<DbSet<Job>>();

        SetupMockDbSet(mockUserSet, users);
        SetupMockDbSet(mockResumeSet, resumes);
        SetupMockDbSet(mockJobSet, jobs);

        _context.Users.Returns(mockUserSet);
        _context.Resumes.Returns(mockResumeSet);
        _context.Jobs.Returns(mockJobSet);

        var command = new CreateJobCommand(
            "Software Engineer",
            "Job description",
            "https://job.com",
            "https://company.com",
            AIModelType.OpenAI);

        // Act
        Result<Guid> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        _context.Jobs.Received(1).Add(Arg.Is<Job>(j => j.PreferredAIModel == AIModelType.OpenAI));
        await _context.Received(1).SaveChangesAsync(Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task Handle_WithoutPreferredAIModel_ShouldSetNullAIModel()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var resumeId = Guid.NewGuid();
        
        _userContext.UserId.Returns(userId);

        var user = new User { Id = userId, Email = "<EMAIL>" };
        var resume = new Resume { Id = resumeId, UserId = userId, ParentId = null };

        var users = new List<User> { user }.AsQueryable();
        var resumes = new List<Resume> { resume }.AsQueryable();
        var jobs = new List<Job>().AsQueryable();

        var mockUserSet = Substitute.For<DbSet<User>>();
        var mockResumeSet = Substitute.For<DbSet<Resume>>();
        var mockJobSet = Substitute.For<DbSet<Job>>();

        SetupMockDbSet(mockUserSet, users);
        SetupMockDbSet(mockResumeSet, resumes);
        SetupMockDbSet(mockJobSet, jobs);

        _context.Users.Returns(mockUserSet);
        _context.Resumes.Returns(mockResumeSet);
        _context.Jobs.Returns(mockJobSet);

        var command = new CreateJobCommand(
            "Software Engineer",
            "Job description",
            "https://job.com",
            "https://company.com");

        // Act
        Result<Guid> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        _context.Jobs.Received(1).Add(Arg.Is<Job>(j => j.PreferredAIModel == null));
        await _context.Received(1).SaveChangesAsync(Arg.Any<CancellationToken>());
    }

    private static void SetupMockDbSet<T>(DbSet<T> mockSet, IQueryable<T> data) where T : class
    {
        mockSet.AsQueryable().Returns(data);
        mockSet.AsNoTracking().Returns(mockSet);
        
        // Setup async methods
        var asyncEnumerable = new TestAsyncEnumerable<T>(data);
        ((IQueryable<T>)mockSet).Provider.Returns(asyncEnumerable.AsAsyncQueryProvider());
        ((IQueryable<T>)mockSet).Expression.Returns(data.Expression);
        ((IQueryable<T>)mockSet).ElementType.Returns(data.ElementType);
        ((IQueryable<T>)mockSet).GetEnumerator().Returns(data.GetEnumerator());
    }
}

// Helper classes for async testing
public class TestAsyncEnumerable<T> : IAsyncEnumerable<T>
{
    private readonly IEnumerable<T> _enumerable;

    public TestAsyncEnumerable(IEnumerable<T> enumerable)
    {
        _enumerable = enumerable;
    }

    public IAsyncEnumerator<T> GetAsyncEnumerator(CancellationToken cancellationToken = default)
    {
        return new TestAsyncEnumerator<T>(_enumerable.GetEnumerator());
    }

    public IQueryProvider AsAsyncQueryProvider()
    {
        return new TestAsyncQueryProvider<T>(_enumerable.AsQueryable().Provider);
    }
}

public class TestAsyncEnumerator<T> : IAsyncEnumerator<T>
{
    private readonly IEnumerator<T> _enumerator;

    public TestAsyncEnumerator(IEnumerator<T> enumerator)
    {
        _enumerator = enumerator;
    }

    public T Current => _enumerator.Current;

    public ValueTask<bool> MoveNextAsync()
    {
        return ValueTask.FromResult(_enumerator.MoveNext());
    }

    public ValueTask DisposeAsync()
    {
        _enumerator.Dispose();
        return ValueTask.CompletedTask;
    }
}

public class TestAsyncQueryProvider<T> : IQueryProvider
{
    private readonly IQueryProvider _inner;

    public TestAsyncQueryProvider(IQueryProvider inner)
    {
        _inner = inner;
    }

    public IQueryable CreateQuery(System.Linq.Expressions.Expression expression)
    {
        return new TestAsyncEnumerable<T>(_inner.CreateQuery<T>(expression));
    }

    public IQueryable<TElement> CreateQuery<TElement>(System.Linq.Expressions.Expression expression)
    {
        return new TestAsyncEnumerable<TElement>(_inner.CreateQuery<TElement>(expression));
    }

    public object Execute(System.Linq.Expressions.Expression expression)
    {
        return _inner.Execute(expression);
    }

    public TResult Execute<TResult>(System.Linq.Expressions.Expression expression)
    {
        return _inner.Execute<TResult>(expression);
    }
}
