﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class RefactorAICustomizationToJobApplication : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ai_confidence_score",
                schema: "public",
                table: "jobs");

            migrationBuilder.DropColumn(
                name: "ai_customization_summary",
                schema: "public",
                table: "jobs");

            migrationBuilder.DropColumn(
                name: "ai_customized_content",
                schema: "public",
                table: "jobs");

            migrationBuilder.DropColumn(
                name: "ai_processed_at",
                schema: "public",
                table: "jobs");

            migrationBuilder.AddColumn<double>(
                name: "ai_confidence_score",
                schema: "public",
                table: "job_applications",
                type: "double precision",
                precision: 3,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ai_customization_summary",
                schema: "public",
                table: "job_applications",
                type: "character varying(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ai_customized_content",
                schema: "public",
                table: "job_applications",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ai_processed_at",
                schema: "public",
                table: "job_applications",
                type: "timestamp with time zone",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ai_confidence_score",
                schema: "public",
                table: "job_applications");

            migrationBuilder.DropColumn(
                name: "ai_customization_summary",
                schema: "public",
                table: "job_applications");

            migrationBuilder.DropColumn(
                name: "ai_customized_content",
                schema: "public",
                table: "job_applications");

            migrationBuilder.DropColumn(
                name: "ai_processed_at",
                schema: "public",
                table: "job_applications");

            migrationBuilder.AddColumn<double>(
                name: "ai_confidence_score",
                schema: "public",
                table: "jobs",
                type: "double precision",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ai_customization_summary",
                schema: "public",
                table: "jobs",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ai_customized_content",
                schema: "public",
                table: "jobs",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ai_processed_at",
                schema: "public",
                table: "jobs",
                type: "timestamp with time zone",
                nullable: true);
        }
    }
}
