using Domain.JobApplications;
using Microsoft.Extensions.Logging;
using SharedKernel;

namespace Application.JobApplications.EventHandlers;

internal sealed class JobApplicationAICustomizationCompletedDomainEventHandler(
    ILogger<JobApplicationAICustomizationCompletedDomainEventHandler> logger)
    : IDomainEventHandler<JobApplicationAICustomizationCompletedDomainEvent>
{
    public Task Handle(JobApplicationAICustomizationCompletedDomainEvent domainEvent, CancellationToken cancellationToken)
    {
        logger.LogInformation(
            "AI customization completed for JobApplication {JobApplicationId} with confidence score {ConfidenceScore}",
            domainEvent.JobApplicationId,
            domainEvent.ConfidenceScore);

        // TODO: Add any additional logic needed when AI customization is completed
        // For example: send notifications, update analytics, etc.

        return Task.CompletedTask;
    }
}
