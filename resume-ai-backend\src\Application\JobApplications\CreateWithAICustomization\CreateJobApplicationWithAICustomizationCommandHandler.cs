using Application.Abstractions.Authentication;
using Application.Abstractions.Data;
using Application.Abstractions.Messaging;
using Domain.JobApplications;
using Domain.Jobs;
using Domain.Resumes;
using Microsoft.EntityFrameworkCore;
using SharedKernel;

namespace Application.JobApplications.CreateWithAICustomization;

internal sealed class CreateJobApplicationWithAICustomizationCommandHandler(
    IApplicationDbContext context,
    IUserContext userContext)
    : ICommandHandler<CreateJobApplicationWithAICustomizationCommand, Guid>
{
    public async Task<Result<Guid>> Handle(
        CreateJobApplicationWithAICustomizationCommand command, 
        CancellationToken cancellationToken)
    {
        // Verify job exists
        Job? job = await context.Jobs
            .AsNoTracking()
            .FirstOrDefaultAsync(j => j.Id == command.JobId && !j.IsDeleted, cancellationToken);

        if (job is null)
        {
            return Result.Failure<Guid>(JobApplicationErrors.JobNotFound(command.JobId));
        }

        // Verify resume exists
        Resume? resume = await context.Resumes
            .AsNoTracking()
            .FirstOrDefaultAsync(r => r.Id == command.CustomizedResumeId && !r.IsDeleted, cancellationToken);

        if (resume is null)
        {
            return Result.Failure<Guid>(JobApplicationErrors.ResumeNotFound(command.CustomizedResumeId));
        }

        // Check if application already exists for this resume-job combination
        bool applicationExists = await context.JobApplications
            .AnyAsync(ja => ja.ResumeId == command.CustomizedResumeId && ja.JobId == command.JobId, cancellationToken);

        if (applicationExists)
        {
            return Result.Failure<Guid>(JobApplicationErrors.AlreadyExists(command.CustomizedResumeId, command.JobId));
        }

        // Create the job application using domain factory method
        var jobApplication = JobApplication.Create(command.CustomizedResumeId, command.JobId, userContext.UserId);

        // Update with AI customization results
        jobApplication.UpdateAICustomization(
            command.AICustomizedContent,
            command.AICustomizationSummary,
            command.AIConfidenceScore);

        context.JobApplications.Add(jobApplication);
        await context.SaveChangesAsync(cancellationToken);

        return jobApplication.Id;
    }
}
