using Application.Abstractions.Data;
using Application.Abstractions.Messaging;
using Application.Services;
using Domain.Resumes;
using Microsoft.EntityFrameworkCore;
using SharedKernel;

namespace Application.Resumes.CreateFromAICustomization;

internal sealed class CreateResumeFromAICustomizationCommandHandler(
    IApplicationDbContext context,
    IHtmlContentProcessor htmlContentProcessor)
    : ICommandHandler<CreateResumeFromAICustomizationCommand, Guid>
{
    public async Task<Result<Guid>> Handle(
        CreateResumeFromAICustomizationCommand command, 
        CancellationToken cancellationToken)
    {
        // Validate parent resume exists and belongs to user
        Resume? parentResume = await context.Resumes
            .AsNoTracking()
            .FirstOrDefaultAsync(r => r.Id == command.ParentResumeId &&
                                     r.UserId == command.UserId &&
                                     r.ParentId == null &&
                                     !r.IsDeleted, cancellationToken);

        if (parentResume is null)
        {
            return Result.Failure<Guid>(ResumeErrors.ParentResumeNotFound(command.ParentResumeId));
        }

        // Process HTML content for security and consistency
        string processedContent;
        try
        {
            processedContent = htmlContentProcessor.ProcessHtmlContent(command.CustomizedContent);
        }
        catch (Exception)
        {
            return Result.Failure<Guid>(ResumeErrors.InvalidContent());
        }

        // Validate processed content
        if (string.IsNullOrWhiteSpace(processedContent))
        {
            return Result.Failure<Guid>(ResumeErrors.EmptyContent());
        }

        var customizedResume = new Resume
        {
            UserId = command.UserId,
            ParentId = command.ParentResumeId,
            ResumeContent = processedContent
        };

        // Raise domain event for resume creation
        customizedResume.Raise(new ResumeCreatedDomainEvent(customizedResume.Id));

        // Raise domain event for AI customization completion
        customizedResume.Raise(new ResumeCustomizedByAIDomainEvent(
            customizedResume.Id, 
            command.ParentResumeId, 
            command.CustomizationSummary));

        context.Resumes.Add(customizedResume);
        await context.SaveChangesAsync(cancellationToken);

        return customizedResume.Id;
    }
}
