using Application.Abstractions.Authentication;
using Application.Abstractions.Data;
using Application.Abstractions.Messaging;
using Domain.JobApplications;
using Microsoft.EntityFrameworkCore;
using SharedKernel;

namespace Application.JobApplications.GetById;

internal sealed class GetJobApplicationByIdQueryHandler(
    IApplicationDbContext context,
    IUserContext userContext)
    : IQueryHandler<GetJobApplicationByIdQuery, JobApplicationResponse>
{
    public async Task<Result<JobApplicationResponse>> Handle(GetJobApplicationByIdQuery query, CancellationToken cancellationToken)
    {
        JobApplication? jobApplication = await context.JobApplications
            .AsNoTracking()
            .FirstOrDefaultAsync(ja => ja.Id == query.JobApplicationId, cancellationToken);

        if (jobApplication is null)
        {
            return Result.Failure<JobApplicationResponse>(JobApplicationErrors.NotFound(query.JobApplicationId));
        }

        // Authorization check - only the creator can view the job application
        if (userContext.UserId != jobApplication.CreatedBy)
        {
            return Result.Failure<JobApplicationResponse>(JobApplicationErrors.NotFound(query.JobApplicationId)); // Return NotFound for security
        }

        var response = new JobApplicationResponse(
            jobApplication.Id,
            jobApplication.ResumeId,
            jobApplication.JobId,
            jobApplication.CreatedBy,
            jobApplication.Status,
            jobApplication.AICustomizedContent,
            jobApplication.AICustomizationSummary,
            jobApplication.AIConfidenceScore,
            jobApplication.AIProcessedAt,
            jobApplication.CreatedAt,
            jobApplication.LastModifiedAt);

        return response;
    }
}
