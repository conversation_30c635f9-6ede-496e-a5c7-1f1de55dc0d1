using Application.Abstractions.AI;
using Domain.AI;

namespace Infrastructure.AI;

public sealed class AIServiceOptions
{
    public const string GeminiSectionName = "AI:Gemini";
    public const string OpenAISectionName = "AI:OpenAI";
    public const string AIModelsSectionName = "AI:Models";

    public string Endpoint { get; set; } = string.Empty;
    public string Api<PERSON>ey { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
    public int TimeoutSeconds { get; set; }
}

/// <summary>
/// Configuration for multiple AI models
/// </summary>
public sealed class AIModelsOptions
{
    public const string SectionName = AIServiceOptions.AIModelsSectionName;

    /// <summary>
    /// Default AI model to use when none is specified
    /// </summary>
    public AIModelType DefaultModel { get; set; } = AIModelType.Gemini;

    /// <summary>
    /// Configuration for Google Gemini
    /// </summary>
    public AIModelConfiguration Gemini { get; set; } = new();

    /// <summary>
    /// Configuration for OpenAI
    /// </summary>
    public AIModelConfiguration OpenAI { get; set; } = new();

    /// <summary>
    /// Gets the configuration for a specific AI model type
    /// </summary>
    public AIModelConfiguration GetModelConfiguration(AIModelType modelType)
    {
        return modelType switch
        {
            AIModelType.Gemini => Gemini,
            AIModelType.OpenAI => OpenAI,
            _ => throw new ArgumentException($"Unsupported AI model type: {modelType}", nameof(modelType))
        };
    }
}
