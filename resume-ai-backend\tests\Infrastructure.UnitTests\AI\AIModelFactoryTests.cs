using Application.Abstractions.AI;
using Domain.AI;
using FluentAssertions;
using Infrastructure.AI;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.Google;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using NSubstitute;
using SharedKernel;

namespace Infrastructure.UnitTests.AI;

public class AIModelFactoryTests
{
    private readonly ILogger<AIModelFactory> _logger;
    private readonly AIModelFactory _factory;

    public AIModelFactoryTests()
    {
        _logger = Substitute.For<ILogger<AIModelFactory>>();
    }

    [Fact]
    public void CreateChatCompletionService_WithDefaultModel_ShouldReturnGeminiService()
    {
        // Arrange
        var options = CreateValidOptions();
        var factory = new AIModelFactory(Options.Create(options), _logger);

        // Act
        Result<IChatCompletionService> result = factory.CreateChatCompletionService();

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().BeOfType<GoogleAIGeminiChatCompletionService>();
    }

    [Fact]
    public void CreateChatCompletionService_WithGeminiModel_ShouldReturnGeminiService()
    {
        // Arrange
        var options = CreateValidOptions();
        var factory = new AIModelFactory(Options.Create(options), _logger);

        // Act
        Result<IChatCompletionService> result = factory.CreateChatCompletionService(AIModelType.Gemini);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().BeOfType<GoogleAIGeminiChatCompletionService>();
    }

    [Fact]
    public void CreateChatCompletionService_WithOpenAIModel_ShouldReturnOpenAIService()
    {
        // Arrange
        var options = CreateValidOptions();
        options.OpenAI.IsEnabled = true;
        var factory = new AIModelFactory(Options.Create(options), _logger);

        // Act
        Result<IChatCompletionService> result = factory.CreateChatCompletionService(AIModelType.OpenAI);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().BeOfType<OpenAIChatCompletionService>();
    }

    [Fact]
    public void CreateChatCompletionService_WithUnsupportedModel_ShouldReturnFailure()
    {
        // Arrange
        var options = CreateValidOptions();
        options.OpenAI.IsEnabled = false;
        var factory = new AIModelFactory(Options.Create(options), _logger);

        // Act
        Result<IChatCompletionService> result = factory.CreateChatCompletionService(AIModelType.OpenAI);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("AIModelFactory.ModelNotSupported");
    }

    [Fact]
    public void CreateChatCompletionService_WithMissingApiKey_ShouldReturnFailure()
    {
        // Arrange
        var options = CreateValidOptions();
        options.Gemini.ApiKey = string.Empty;
        var factory = new AIModelFactory(Options.Create(options), _logger);

        // Act
        Result<IChatCompletionService> result = factory.CreateChatCompletionService(AIModelType.Gemini);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("AIModelFactory.CreationFailed");
    }

    [Fact]
    public void GetDefaultModelType_ShouldReturnConfiguredDefault()
    {
        // Arrange
        var options = CreateValidOptions();
        options.DefaultModel = AIModelType.OpenAI;
        var factory = new AIModelFactory(Options.Create(options), _logger);

        // Act
        AIModelType defaultModel = factory.GetDefaultModelType();

        // Assert
        defaultModel.Should().Be(AIModelType.OpenAI);
    }

    [Fact]
    public void IsModelSupported_WithValidConfiguration_ShouldReturnTrue()
    {
        // Arrange
        var options = CreateValidOptions();
        var factory = new AIModelFactory(Options.Create(options), _logger);

        // Act
        bool isSupported = factory.IsModelSupported(AIModelType.Gemini);

        // Assert
        isSupported.Should().BeTrue();
    }

    [Fact]
    public void IsModelSupported_WithDisabledModel_ShouldReturnFalse()
    {
        // Arrange
        var options = CreateValidOptions();
        options.Gemini.IsEnabled = false;
        var factory = new AIModelFactory(Options.Create(options), _logger);

        // Act
        bool isSupported = factory.IsModelSupported(AIModelType.Gemini);

        // Assert
        isSupported.Should().BeFalse();
    }

    [Fact]
    public void IsModelSupported_WithMissingApiKey_ShouldReturnFalse()
    {
        // Arrange
        var options = CreateValidOptions();
        options.Gemini.ApiKey = string.Empty;
        var factory = new AIModelFactory(Options.Create(options), _logger);

        // Act
        bool isSupported = factory.IsModelSupported(AIModelType.Gemini);

        // Assert
        isSupported.Should().BeFalse();
    }

    [Fact]
    public void GetSupportedModels_WithValidConfigurations_ShouldReturnEnabledModels()
    {
        // Arrange
        var options = CreateValidOptions();
        options.OpenAI.IsEnabled = true;
        var factory = new AIModelFactory(Options.Create(options), _logger);

        // Act
        IEnumerable<AIModelType> supportedModels = factory.GetSupportedModels();

        // Assert
        supportedModels.Should().Contain(AIModelType.Gemini);
        supportedModels.Should().Contain(AIModelType.OpenAI);
    }

    [Fact]
    public void GetSupportedModels_WithOnlyGeminiEnabled_ShouldReturnOnlyGemini()
    {
        // Arrange
        var options = CreateValidOptions();
        options.OpenAI.IsEnabled = false;
        var factory = new AIModelFactory(Options.Create(options), _logger);

        // Act
        IEnumerable<AIModelType> supportedModels = factory.GetSupportedModels();

        // Assert
        supportedModels.Should().Contain(AIModelType.Gemini);
        supportedModels.Should().NotContain(AIModelType.OpenAI);
    }

    private static AIModelsOptions CreateValidOptions()
    {
        return new AIModelsOptions
        {
            DefaultModel = AIModelType.Gemini,
            Gemini = new AIModelConfiguration
            {
                ApiKey = "test-gemini-key",
                Model = "gemini-2.5-pro",
                IsEnabled = true,
                TimeoutSeconds = 180
            },
            OpenAI = new AIModelConfiguration
            {
                ApiKey = "test-openai-key",
                Model = "gpt-4",
                IsEnabled = false,
                TimeoutSeconds = 180
            }
        };
    }
}
