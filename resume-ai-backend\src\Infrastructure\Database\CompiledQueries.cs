using Domain.Resumes;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Database;

/// <summary>
/// Contains compiled queries for improved performance across the application
/// </summary>
public static class CompiledQueries
{
    /// <summary>
    /// Gets the base (parent) resume for a user. This is the resume with ParentId = null.
    /// </summary>
    /// <param name="context">The database context</param>
    /// <param name="userId">The user ID to search for</param>
    /// <returns>The parent resume or null if not found</returns>
    public static readonly Func<ApplicationDbContext, Guid, Task<Resume?>> GetBaseReport =
        EF.CompileAsyncQuery((ApplicationDbContext context, Guid userId) =>
            context.Resumes
                .AsNoTracking()
                .FirstOrDefault(r => r.UserId == userId && 
                                   r.ParentId == null && 
                                   !r.IsDeleted));

    /// <summary>
    /// Gets the base (parent) resume for a user with content included.
    /// </summary>
    /// <param name="context">The database context</param>
    /// <param name="userId">The user ID to search for</param>
    /// <returns>The parent resume with content or null if not found</returns>
    public static readonly Func<ApplicationDbContext, Guid, Task<Resume?>> GetBaseReportWithContent =
        EF.CompileAsyncQuery((ApplicationDbContext context, Guid userId) =>
            context.Resumes
                .AsNoTracking()
                .FirstOrDefault(r => r.UserId == userId && 
                                   r.ParentId == null && 
                                   !r.IsDeleted));

    /// <summary>
    /// Gets all customized resumes for a user (resumes with ParentId != null).
    /// </summary>
    /// <param name="context">The database context</param>
    /// <param name="userId">The user ID to search for</param>
    /// <returns>List of customized resumes</returns>
    public static readonly Func<ApplicationDbContext, Guid, Task<List<Resume>>> GetCustomizedResumes =
        EF.CompileAsyncQuery((ApplicationDbContext context, Guid userId) =>
            context.Resumes
                .AsNoTracking()
                .Where(r => r.UserId == userId && 
                           r.ParentId != null && 
                           !r.IsDeleted)
                .OrderByDescending(r => r.CreatedAt)
                .ToList());
}
