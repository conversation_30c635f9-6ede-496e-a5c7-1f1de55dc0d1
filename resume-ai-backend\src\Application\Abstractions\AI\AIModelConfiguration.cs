namespace Application.Abstractions.AI;

/// <summary>
/// Configuration for AI model settings
/// </summary>
public sealed class AIModelConfiguration
{
    public string ApiKey { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
    public string? Endpoint { get; set; }
    public int TimeoutSeconds { get; set; } = 180;
    public bool IsEnabled { get; set; } = true;
}
