using Domain.AI;
using Microsoft.SemanticKernel.ChatCompletion;
using SharedKernel;

namespace Application.Abstractions.AI;

/// <summary>
/// Factory interface for creating AI model instances based on model type
/// </summary>
public interface IAIModelFactory
{
    /// <summary>
    /// Creates a chat completion service for the specified AI model type
    /// </summary>
    /// <param name="modelType">The AI model type to create. If null, uses the default model.</param>
    /// <returns>A result containing the chat completion service or an error</returns>
    Result<IChatCompletionService> CreateChatCompletionService(AIModelType? modelType = null);
    
    /// <summary>
    /// Gets the default AI model type
    /// </summary>
    AIModelType GetDefaultModelType();
    
    /// <summary>
    /// Checks if the specified AI model type is supported and properly configured
    /// </summary>
    /// <param name="modelType">The AI model type to validate</param>
    /// <returns>True if the model is supported and configured, false otherwise</returns>
    bool IsModelSupported(AIModelType modelType);
    
    /// <summary>
    /// Gets all supported AI model types
    /// </summary>
    /// <returns>Collection of supported AI model types</returns>
    IEnumerable<AIModelType> GetSupportedModels();
}



/// <summary>
/// Errors related to AI model factory operations
/// </summary>
public static class AIModelFactoryErrors
{
    public static Error ModelNotSupported(AIModelType modelType) => Error.Problem(
        "AIModelFactory.ModelNotSupported",
        $"The AI model '{modelType}' is not supported or not properly configured");
    
    public static Error ModelConfigurationInvalid(AIModelType modelType) => Error.Problem(
        "AIModelFactory.ConfigurationInvalid",
        $"The configuration for AI model '{modelType}' is invalid or missing");
    
    public static Error ModelCreationFailed(AIModelType modelType, string reason) => Error.Problem(
        "AIModelFactory.CreationFailed",
        $"Failed to create AI model '{modelType}': {reason}");
    
    public static Error NoSupportedModels() => Error.Problem(
        "AIModelFactory.NoSupportedModels",
        "No AI models are properly configured and available");
}
