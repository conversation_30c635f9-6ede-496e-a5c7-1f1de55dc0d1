using Application.Abstractions.AI;
using Application.Abstractions.Data;
using Application.Abstractions.Messaging;
using Application.JobApplications.CreateWithAICustomization;
using Application.Resumes.CreateFromAICustomization;
using Domain.Jobs;
using Hangfire;
using Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Infrastructure.BackgroundJobs;

public class AIResumeCustomizationJob
{
    private readonly IAIResumeCustomizationService _aiService;
    private readonly IApplicationDbContext _dbContext;
    private readonly ICommandHandler<CreateResumeFromAICustomizationCommand, Guid> _createResumeHandler;
    private readonly ICommandHandler<CreateJobApplicationWithAICustomizationCommand, Guid> _createJobApplicationHandler;
    private readonly ILogger<AIResumeCustomizationJob> _logger;

    public AIResumeCustomizationJob(
        IAIResumeCustomizationService aiService,
        IApplicationDbContext dbContext,
        ICommandHandler<CreateResumeFromAICustomizationCommand, Guid> createResumeHand<PERSON>,
        ICommandHandler<CreateJobApplicationWithAICustomizationCommand, Guid> createJobApplicationHandler,
        ILogger<AIResumeCustomizationJob> logger)
    {
        _aiService = aiService;
        _dbContext = dbContext;
        _createResumeHandler = createResumeHandler;
        _createJobApplicationHandler = createJobApplicationHandler;
        _logger = logger;
    }

    [AutomaticRetry(Attempts = 2, DelaysInSeconds = [60, 300])]
    public async Task ProcessAICustomizationAsync(Guid jobId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting AI resume customization background job for Job ID: {JobId}", jobId);

        try
        {
            // Retrieve the job from database
            var job = await _dbContext.Jobs
                .FirstOrDefaultAsync(j => j.Id == jobId, cancellationToken);

            if (job == null)
            {
                _logger.LogError("Job with ID {JobId} not found", jobId);
                return;
            }

            // Get the user's parent resume using compiled query
            var parentResume = await CompiledQueries.GetBaseReportWithContent(
                (ApplicationDbContext)_dbContext,
                job.UserId);

            if (parentResume == null)
            {
                _logger.LogError("No parent resume found for user {UserId} of job {JobId}", job.UserId, jobId);
                return;
            }

            // Check if job is already processed
            if (job.Status == JobStatus.AIProcessingCompleted)
            {
                _logger.LogInformation("Job {JobId} is already processed", jobId);
                return;
            }

            // Update job status to processing
            job.UpdateStatus(JobStatus.AIProcessing);
            await _dbContext.SaveChangesAsync(cancellationToken);

            // Create AI customization request with preferred AI model
            var aiRequest = new AICustomizationRequest(
                job.JobTitle,
                job.JobDescription,
                job.CompanyUrl,
                parentResume.ResumeContent,
                job.PreferredAIModel);

            // Process AI customization (this can take several minutes)
            var result = await _aiService.CustomizeResumeAsync(aiRequest, cancellationToken);

            if (result.IsSuccess)
            {
                // Create a new customized resume
                var createResumeCommand = new CreateResumeFromAICustomizationCommand(
                    job.UserId,
                    parentResume.Id,
                    result.Value.CustomizedResumeContent,
                    result.Value.CustomizationSummary,
                    job.PreferredAIModel);

                var resumeResult = await _createResumeHandler.Handle(createResumeCommand, cancellationToken);

                if (resumeResult.IsSuccess)
                {
                    // Create job application with AI customization results
                    var createJobApplicationCommand = new CreateJobApplicationWithAICustomizationCommand(
                        jobId,
                        job.UserId,
                        resumeResult.Value,
                        result.Value.CustomizedResumeContent,
                        result.Value.CustomizationSummary,
                        result.Value.ConfidenceScore,
                        job.PreferredAIModel);

                    var jobApplicationResult = await _createJobApplicationHandler.Handle(createJobApplicationCommand, cancellationToken);

                    if (jobApplicationResult.IsSuccess)
                    {
                        job.UpdateStatus(JobStatus.AIProcessingCompleted);

                        _logger.LogInformation(
                            "AI resume customization completed successfully for Job ID: {JobId} with confidence: {Confidence}. Created Resume: {ResumeId}, JobApplication: {JobApplicationId}",
                            jobId, result.Value.ConfidenceScore, resumeResult.Value, jobApplicationResult.Value);
                    }
                    else
                    {
                        job.UpdateStatus(JobStatus.AIProcessingFailed);
                        _logger.LogError(
                            "Failed to create job application for Job ID: {JobId}. Error: {Error}",
                            jobId, jobApplicationResult.Error.Description);
                    }
                }
                else
                {
                    job.UpdateStatus(JobStatus.AIProcessingFailed);
                    _logger.LogError(
                        "Failed to create customized resume for Job ID: {JobId}. Error: {Error}",
                        jobId, resumeResult.Error.Description);
                }
            }
            else
            {
                // Update job status to failed
                job.UpdateStatus(JobStatus.AIProcessingFailed);

                _logger.LogError(
                    "AI resume customization failed for Job ID: {JobId}. Error: {Error}",
                    jobId, result.Error.Description);
            }

            // Save changes to database
            await _dbContext.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("AI resume customization background job completed for Job ID: {JobId}", jobId);
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("AI resume customization job for Job ID: {JobId} was cancelled", jobId);
            
            // Update job status to failed due to cancellation
            await UpdateJobStatusSafely(jobId, JobStatus.AIProcessingFailed, cancellationToken);
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error in AI resume customization job for Job ID: {JobId}", jobId);
            
            // Update job status to failed
            await UpdateJobStatusSafely(jobId, JobStatus.AIProcessingFailed, cancellationToken);
            throw;
        }
    }

    private async Task UpdateJobStatusSafely(Guid jobId, JobStatus status, CancellationToken cancellationToken)
    {
        try
        {
            var job = await _dbContext.Jobs.FirstOrDefaultAsync(j => j.Id == jobId, cancellationToken);
            if (job != null)
            {
                job.UpdateStatus(status);
                await _dbContext.SaveChangesAsync(cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update job status for Job ID: {JobId}", jobId);
        }
    }
}
