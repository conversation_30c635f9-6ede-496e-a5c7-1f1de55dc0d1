using Application.Abstractions.Messaging;
using Application.JobApplications.CreateApplication;
using SharedKernel;
using Web.Api.Extensions;
using Web.Api.Infrastructure;

namespace Web.Api.Endpoints.JobApplications;

internal sealed class Create : IEndpoint
{
    public sealed record CreateJobApplicationRequest(
        Guid ResumeId,
        Guid JobId);

    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("job-applications", async (
            CreateJobApplicationRequest request,
            ICommandHandler<CreateJobApplicationCommand, Guid> handler,
            CancellationToken cancellationToken) =>
        {
            // The user ID will be resolved in the command handler through IUserContext
            var command = new CreateJobApplicationCommand(
                request.ResumeId,
                request.JobId,
                Guid.Empty); // This will be set by the handler using IUserContext

            Result<Guid> result = await handler.Handle(command, cancellationToken);

            return result.Match(
                jobApplicationId => Results.Created($"/job-applications/{jobApplicationId}", jobApplicationId),
                CustomResults.Problem);
        })
        .WithTags(Tags.JobApplications)
        .WithName("CreateJobApplication")
        .Produces<Guid>(StatusCodes.Status201Created)
        .ProducesValidationProblem()
        .RequireAuthorization();
    }
}
